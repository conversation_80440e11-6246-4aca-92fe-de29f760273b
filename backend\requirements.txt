# Enterprise AI/ML Platform - Backend Dependencies
# Production-ready Python packages with pinned versions

# Core Framework
fastapi
uvicorn[standard]
gunicorn
pydantic
pydantic-settings

# Database & ORM
sqlalchemy[asyncio]
asyncpg
alembic
motor
pymongo

# Caching & Message Queue
redis[hiredis]
celery[redis]
kombu

# Authentication & Security
python-jose[cryptography]
passlib[bcrypt]
python-multipart
cryptography

# HTTP Client & Utilities
httpx
aiofiles
python-dotenv

# Data Processing & ML Core
numpy
pandas
scikit-learn
scipy

# Deep Learning Frameworks
torch
torchvision
torchaudio
tensorflow
tensorflow-probability

# AutoML & Advanced ML
autogluon
optuna
hyperopt
bayesian-optimization

# Computer Vision
opencv-python
Pillow
albumentations
timm

# Natural Language Processing
transformers
tokenizers
datasets
sentence-transformers
spacy
nltk

# Generative AI
diffusers
accelerate
xformers

# Time Series
prophet
statsmodels
pmdarima
sktime

# Reinforcement Learning
stable-baselines3
gymnasium
ale-py

# Graph Neural Networks
torch-geometric
networkx
igraph

# Quantum Computing
qiskit
qiskit-aer
qiskit-optimization

# Audio Processing
librosa
soundfile
speechrecognition
pydub

# Data Visualization & Plotting
plotly
matplotlib
seaborn

# Model Interpretability
shap
lime

# MLOps & Experiment Tracking
mlflow
wandb
dvc

# Monitoring & Observability
prometheus-client
prometheus-fastapi-instrumentator
structlog

# API Documentation
Markdown

# Testing & Development
pytest
pytest-asyncio
pytest-cov
httpx
faker

# Code Quality
black
isort
flake8
mypy
bandit
safety

# Performance & Optimization
numba
cython

# Cloud & Storage
boto3
google-cloud-storage
azure-storage-blob

# Utilities
click
tqdm
python-dateutil
pytz
requests

# Email
emails

# Rate Limiting
slowapi

# WebSocket
websockets

# Image Processing
imageio
scikit-image

# Feature Engineering
feature-engine
category-encoders

# Model Serving
onnx
onnxruntime

# Federated Learning
flower

# Edge AI
tensorrt
openvino

# Additional Scientific Computing
sympy
joblib
dask

# Configuration Management
dynaconf

# Async Database Drivers
aiosqlite
aiomysql

# Serialization
orjson
msgpack

# Validation
cerberus
marshmallow

# Caching
diskcache

# Compression
lz4
zstandard

# Profiling
py-spy
memory-profiler

# Documentation
sphinx
sphinx-rtd-theme
